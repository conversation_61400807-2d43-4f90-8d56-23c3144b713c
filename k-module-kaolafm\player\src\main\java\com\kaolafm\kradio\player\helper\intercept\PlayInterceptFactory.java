package com.kaolafm.kradio.player.helper.intercept;


import android.util.Log;

import com.kaolafm.opensdk.player.core.listener.IPlayIntercept;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.util.ArrayList;

/**
 * 播放开始拦截器
 */
public class PlayInterceptFactory {

    public static int TEMP_TASK_TYPE_CLOCK = 10;
    public static int TEMP_TASK_TYPE_AD = 20;
    public static int TEMP_TASK_TYPE_HINT = 30;
    public static int TEMP_TASK_TYPE_OVERHAUL = 40;

    private static ArrayList<BasePlayChainIntercept> basePlayChainInterceptArrayList;
    private IPlayIntercept mIPlayIntercept;
    public CrashMessageChainIntercept crashMessageChainIntercept;
    public AdPlayChainIntercept adPlayChainIntercept;
    //    public HistoryPlayChainIntercept historyPlayChainIntercept;
    public ClockChainIntercept clockChainIntercept;
    public LiveStreamChainIntercept liveStreamChainIntercept;
    public HintVoiceChainIntercept hintVoiceChainIntercept;
    public OverhaulIntercept overhaulIntercept;
    private boolean iAllChainEndExecuted = false;
    private long time = 0;

    //上次播放的PlayItem
    private PlayItem lastPlayItem = null;

    static {
        basePlayChainInterceptArrayList = new ArrayList<>();
    }

    public static final class PLAY_INTERCEPT_FACTORY {
        private static final PlayInterceptFactory S_INSTANCE = new PlayInterceptFactory();
    }

    public static PlayInterceptFactory getInstance() {
        return PLAY_INTERCEPT_FACTORY.S_INSTANCE;
    }

    private PlayInterceptFactory() {
        initChainIntercept();
    }

    public void init() {
        initListener();
    }

    private void initListener() {
        PlayerLogUtil.log(getClass().getSimpleName(), "initListener, processId= "+android.os.Process.myTid());
        PlayerCustomizeManager.getInstance().setOnPlayStart((playItem, iPlayIntercept,isPlayNow) -> {
            PlayerLogUtil.log(getClass().getSimpleName(), "initListener.setOnPlayStart, processId= "+android.os.Process.myTid());
            mIPlayIntercept = iPlayIntercept;
            Log.d(getClass().getSimpleName(), "initListener-setOnPlayStart");
//            if (System.currentTimeMillis() - time > 1000) {
//                //这个会有短时间多次回调的问题，会导致重复执行责任链，所以做一个类似点击防抖的处理
//                time = System.currentTimeMillis();
//                PlayerLogUtil.log(getClass().getSimpleName(), "initListener.play, processId= "+android.os.Process.myTid());
//                play(playItem);
//            }
            PlayerLogUtil.log(getClass().getSimpleName(), "initListener.play, processId= "+android.os.Process.myTid());
            play(playItem);

        });
    }

    private void initChainIntercept() {
        // 根据音频广告的优先级进行播放
        // 开屏 》定时 》智能电台 》报时
        crashMessageChainIntercept = new CrashMessageChainIntercept();
//        historyPlayChainIntercept = new HistoryPlayChainIntercept();
        clockChainIntercept = new ClockChainIntercept();
        hintVoiceChainIntercept = new HintVoiceChainIntercept();
        adPlayChainIntercept = new AdPlayChainIntercept();
        liveStreamChainIntercept = new LiveStreamChainIntercept();
        overhaulIntercept = new OverhaulIntercept();

//        basePlayChainInterceptArrayList.add(historyPlayChainIntercept);
        basePlayChainInterceptArrayList.add(crashMessageChainIntercept);
        basePlayChainInterceptArrayList.add(clockChainIntercept);
        basePlayChainInterceptArrayList.add(hintVoiceChainIntercept);
        basePlayChainInterceptArrayList.add(adPlayChainIntercept);
        basePlayChainInterceptArrayList.add(liveStreamChainIntercept);
        basePlayChainInterceptArrayList.add(overhaulIntercept);

    }

    public void play(PlayItem playItem) {
        iAllChainEndExecuted = false;
        int index = 0;
        if (lastPlayItem != null && playItem != null &&
                lastPlayItem.getAudioId() == playItem.getAudioId() &&
                lastPlayItem.getAlbumId().equals(playItem.getAlbumId())) {
            // 播放的是同一个节目
//            index = BasePlayChainIntercept.currentChainIndex;
//            if (index >= basePlayChainInterceptArrayList.size()) {
//                index = 0;
//            }
            PlayerLogUtil.log(getClass().getSimpleName(), "Play the same item, continue the current interceptor " + index);
        } else {
            // 播放的是不同节目
            PlayerLogUtil.log(getClass().getSimpleName(), "Play another item, terminate all interceptor");
            for (BasePlayChainIntercept intercept: basePlayChainInterceptArrayList) {
                intercept.terminate();
            }
        }
        this.lastPlayItem = playItem;
        PlayerLogUtil.log(getClass().getSimpleName(), "Reset all player interceptor");
        for (BasePlayChainIntercept intercept: basePlayChainInterceptArrayList) {
            intercept.reset();
            if (intercept instanceof AdPlayChainIntercept){
                ((AdPlayChainIntercept) intercept).isValid = false;
            }
        }
        BasePlayChainIntercept basePlayChainIntercept = basePlayChainInterceptArrayList.get(index);
        basePlayChainIntercept.play(playItem, index, basePlayChainInterceptArrayList, iAllChainEnd);
    }

    private BasePlayChainIntercept.IAllChainEnd iAllChainEnd = new BasePlayChainIntercept.IAllChainEnd() {
        @Override
        public void allSuccess(PlayItem playItem) {
            if (iAllChainEndExecuted) {
                PlayerLogUtil.log(getClass().getSimpleName(), "allSuccess,iAllChainEndExecuted="+iAllChainEndExecuted+"; processId= "+android.os.Process.myTid());
                return;
            }
            iAllChainEndExecuted = true;
            PlayerLogUtil.log(getClass().getSimpleName(), "app intercept end...");
            PlayerLogUtil.log(getClass().getSimpleName(), "allSuccess, processId= "+android.os.Process.myTid());
            if (mIPlayIntercept != null) {
                PlayerLogUtil.log(getClass().getSimpleName(), "allSuccess.mIPlayIntercept.interceptDone, processId= "+android.os.Process.myTid());
                mIPlayIntercept.interceptDone();
            }
        }
    };
}
